# AI Voice Mate - Complete Setup Guide

This guide will help you set up the complete AI Voice Mate system with both backend and frontend components.

## System Requirements

### Backend Requirements
- Python 3.8+
- WebSocket support
- Audio processing libraries
- Network access on port 5010

### Frontend Requirements
- Node.js 16+
- Modern web browser with:
  - WebSocket support
  - Web Audio API support
  - Microphone access permissions
- HTTPS (required for microphone access in production)

## Quick Start (Development)

### 1. Backend Setup

```bash
# Navigate to the project root
cd ai_voice_mate

# Install Python dependencies (if not already done)
pip install -r requirements.txt

# Start the WebSocket server
python -m tutor.websocket_server
```

The backend will start on `ws://localhost:5010`

### 2. Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

The frontend will open at `http://localhost:3000`

### 3. Test the Connection

1. Open `http://localhost:3000` in your browser
2. Allow microphone access when prompted
3. Check that the connection status shows "Connected"
4. Click the green phone button to start a call
5. Speak into your microphone to test audio transmission

## Production Deployment

### Backend Deployment

1. **Configure the WebSocket Server**
   ```bash
   # Update the server configuration in websocket_server.py
   # Change the host from "0.0.0.0" to your server IP
   # Ensure port 5010 is open in your firewall
   ```

2. **Run with Process Manager**
   ```bash
   # Using systemd (recommended)
   sudo systemctl enable ai-voice-mate-backend
   sudo systemctl start ai-voice-mate-backend
   
   # Or using PM2
   pm2 start "python -m tutor.websocket_server" --name ai-voice-mate-backend
   ```

### Frontend Deployment

#### Option 1: Using the Deployment Script

```bash
cd frontend
chmod +x scripts/deploy.sh
sudo ./scripts/deploy.sh
```

#### Option 2: Manual Deployment

```bash
# Build the application
cd frontend
npm run build

# Copy to web server directory
sudo cp -r build/* /var/www/ai-voice-mate/

# Configure nginx (see nginx.conf example)
sudo cp nginx.conf /etc/nginx/sites-available/ai-voice-mate
sudo ln -s /etc/nginx/sites-available/ai-voice-mate /etc/nginx/sites-enabled/
sudo systemctl reload nginx
```

#### Option 3: Docker Deployment

```bash
# Build and run with Docker
cd frontend
docker-compose up -d
```

## Configuration

### Backend Configuration

Edit `tutor/websocket_server.py`:

```python
# Change the server host and port
start_server = websockets.serve(on_request, "0.0.0.0", 5010)
```

### Frontend Configuration

Create `frontend/.env`:

```env
REACT_APP_WS_URL=ws://your-server.com:5010
REACT_APP_AUTO_CONNECT=true
```

## SSL/HTTPS Setup (Required for Production)

Microphone access requires HTTPS in production. Here's how to set it up:

### 1. Obtain SSL Certificate

```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 2. Update Nginx Configuration

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # Your existing configuration...
}
```

### 3. Update Frontend Configuration

```env
REACT_APP_WS_URL=wss://your-server.com:5010
```

## Troubleshooting

### Common Issues

#### 1. WebSocket Connection Failed
- **Check**: Backend server is running on port 5010
- **Check**: Firewall allows connections on port 5010
- **Check**: WebSocket URL is correct in frontend settings

#### 2. Microphone Access Denied
- **Solution**: Use HTTPS in production
- **Solution**: Check browser permissions
- **Solution**: Try a different browser

#### 3. No Audio During Call
- **Check**: Microphone permissions are granted
- **Check**: Audio levels are showing in the interface
- **Check**: Browser supports Web Audio API
- **Check**: Network connectivity between frontend and backend

#### 4. Poor Audio Quality
- **Check**: Network connection stability
- **Check**: Microphone quality
- **Check**: Browser audio settings

### Debug Mode

Enable debug logging in the frontend:

```env
REACT_APP_DEBUG_AUDIO=true
REACT_APP_LOG_LEVEL=debug
```

Check browser console for detailed logs.

## Browser Compatibility

| Browser | Version | Support |
|---------|---------|---------|
| Chrome  | 66+     | ✅ Full |
| Firefox | 60+     | ✅ Full |
| Safari  | 11.1+   | ✅ Full |
| Edge    | 79+     | ✅ Full |

## Performance Optimization

### Frontend Optimizations

1. **Enable Gzip Compression** (included in nginx.conf)
2. **Use CDN** for static assets
3. **Enable Browser Caching** (configured in nginx.conf)
4. **Optimize Bundle Size**:
   ```bash
   npm run build -- --analyze
   ```

### Backend Optimizations

1. **Use Production WSGI Server**:
   ```bash
   pip install gunicorn
   gunicorn -w 4 -k uvicorn.workers.UvicornWorker tutor.websocket_server:app
   ```

2. **Configure Load Balancing** for multiple instances
3. **Use Redis** for session management (if scaling)

## Monitoring

### Health Checks

Frontend health check endpoint: `http://your-domain.com/health`

Backend health check:
```python
# Add to websocket_server.py
@app.route('/health')
def health_check():
    return {'status': 'healthy', 'timestamp': time.time()}
```

### Logging

Configure logging for both components:

```bash
# Frontend logs (nginx)
tail -f /var/log/nginx/access.log

# Backend logs
tail -f /var/log/ai-voice-mate/websocket.log
```

## Security Considerations

1. **Use HTTPS/WSS** in production
2. **Configure CORS** properly
3. **Implement Rate Limiting**
4. **Use Firewall Rules**
5. **Regular Security Updates**

## Scaling

### Horizontal Scaling

1. **Load Balancer** (nginx, HAProxy)
2. **Multiple Backend Instances**
3. **Session Affinity** for WebSocket connections
4. **Redis** for shared state

### Vertical Scaling

1. **Increase Server Resources**
2. **Optimize Audio Processing**
3. **Database Optimization** (if applicable)

## Support

For additional help:

1. Check the troubleshooting section above
2. Review browser console logs
3. Check server logs
4. Verify network connectivity
5. Test with different browsers/devices

## Next Steps

After successful setup:

1. **Customize the UI** to match your branding
2. **Add Authentication** if required
3. **Implement Call Recording** (if needed)
4. **Add Analytics** and monitoring
5. **Scale** based on usage patterns

## License

This project is licensed under the MIT License. See LICENSE file for details.
