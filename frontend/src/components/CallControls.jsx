import React, { useState } from 'react';
import { 
  Phone, 
  PhoneOff, 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX,
  Loader2
} from 'lucide-react';
import { CALL_STATES } from '../hooks/useVoiceSocket';

const CallControls = ({ 
  callState, 
  isConnected, 
  isMuted, 
  onStartCall, 
  onEndCall, 
  onToggleMute,
  audioLevel = 0 
}) => {
  const [speakerEnabled, setSpeakerEnabled] = useState(true);

  const isInCall = callState === CALL_STATES.IN_CALL;
  const isConnecting = callState === CALL_STATES.CONNECTING;
  const isEnding = callState === CALL_STATES.ENDING;
  const canStartCall = isConnected && callState === CALL_STATES.IDLE;
  const canEndCall = isInCall || isConnecting;

  const handleStartCall = () => {
    if (canStartCall) {
      onStartCall();
    }
  };

  const handleEndCall = () => {
    if (canEndCall) {
      onEndCall();
    }
  };

  const handleToggleMute = () => {
    if (isInCall) {
      onToggleMute();
    }
  };

  const handleToggleSpeaker = () => {
    setSpeakerEnabled(prev => !prev);
    // In a real implementation, you would control audio output routing here
  };

  // Audio level visualization
  const AudioLevelIndicator = () => {
    if (!isInCall || isMuted) return null;

    const bars = 5;
    const activeBarCount = Math.ceil((audioLevel / 100) * bars);

    return (
      <div className="flex items-center space-x-1 ml-2">
        {Array.from({ length: bars }, (_, i) => (
          <div
            key={i}
            className={`w-1 h-4 rounded-full transition-colors duration-150 ${
              i < activeBarCount 
                ? i < 2 
                  ? 'bg-success-500' 
                  : i < 4 
                    ? 'bg-yellow-500' 
                    : 'bg-danger-500'
                : 'bg-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="card p-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Call Controls</h2>
        <p className="text-sm text-gray-600">
          {!isConnected && 'Connect to server to start calling'}
          {isConnected && callState === CALL_STATES.IDLE && 'Ready to start call'}
          {isConnecting && 'Connecting to call...'}
          {isInCall && 'Call in progress'}
          {isEnding && 'Ending call...'}
          {callState === CALL_STATES.ENDED && 'Call ended'}
          {callState === CALL_STATES.ERROR && 'Call error occurred'}
        </p>
      </div>

      {/* Main Call Button */}
      <div className="flex justify-center mb-6">
        {!isInCall && !isConnecting && !isEnding ? (
          <button
            onClick={handleStartCall}
            disabled={!canStartCall}
            className={`
              relative w-16 h-16 rounded-full flex items-center justify-center
              transition-all duration-200 transform hover:scale-105 focus:outline-none
              focus:ring-4 focus:ring-offset-2 shadow-lg
              ${canStartCall 
                ? 'bg-success-600 hover:bg-success-700 focus:ring-success-500 text-white' 
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }
            `}
          >
            <Phone size={24} />
            {canStartCall && (
              <div className="absolute inset-0 rounded-full bg-success-600 animate-ping opacity-20"></div>
            )}
          </button>
        ) : (
          <button
            onClick={handleEndCall}
            disabled={!canEndCall}
            className={`
              relative w-16 h-16 rounded-full flex items-center justify-center
              transition-all duration-200 transform hover:scale-105 focus:outline-none
              focus:ring-4 focus:ring-offset-2 shadow-lg
              ${canEndCall 
                ? 'bg-danger-600 hover:bg-danger-700 focus:ring-danger-500 text-white' 
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }
            `}
          >
            {isEnding ? (
              <Loader2 size={24} className="animate-spin" />
            ) : (
              <PhoneOff size={24} />
            )}
            {isInCall && (
              <div className="absolute inset-0 rounded-full bg-danger-600 animate-pulse opacity-30"></div>
            )}
          </button>
        )}
      </div>

      {/* Secondary Controls */}
      <div className="flex justify-center space-x-4">
        {/* Microphone Control */}
        <div className="flex flex-col items-center">
          <button
            onClick={handleToggleMute}
            disabled={!isInCall}
            className={`
              w-12 h-12 rounded-full flex items-center justify-center
              transition-all duration-200 transform hover:scale-105 focus:outline-none
              focus:ring-2 focus:ring-offset-2 shadow-md
              ${!isInCall 
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed' 
                : isMuted 
                  ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'
              }
            `}
          >
            {isMuted ? <MicOff size={20} /> : <Mic size={20} />}
          </button>
          <div className="flex items-center mt-1">
            <span className="text-xs text-gray-500">
              {isMuted ? 'Muted' : 'Mic'}
            </span>
            <AudioLevelIndicator />
          </div>
        </div>

        {/* Speaker Control */}
        <div className="flex flex-col items-center">
          <button
            onClick={handleToggleSpeaker}
            disabled={!isInCall}
            className={`
              w-12 h-12 rounded-full flex items-center justify-center
              transition-all duration-200 transform hover:scale-105 focus:outline-none
              focus:ring-2 focus:ring-offset-2 shadow-md
              ${!isInCall 
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed' 
                : !speakerEnabled 
                  ? 'bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500'
              }
            `}
          >
            {speakerEnabled ? <Volume2 size={20} /> : <VolumeX size={20} />}
          </button>
          <span className="text-xs text-gray-500 mt-1">
            {speakerEnabled ? 'Speaker' : 'Muted'}
          </span>
        </div>
      </div>

      {/* Call Status Indicators */}
      {isConnecting && (
        <div className="mt-6 flex items-center justify-center space-x-2">
          <Loader2 size={16} className="animate-spin text-primary-600" />
          <span className="text-sm text-primary-600">Connecting to call...</span>
        </div>
      )}

      {isInCall && (
        <div className="mt-6 text-center">
          <div className="inline-flex items-center space-x-2 px-3 py-1 bg-success-100 text-success-800 rounded-full text-sm">
            <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
            <span>Call Active</span>
          </div>
        </div>
      )}

      {callState === CALL_STATES.ERROR && (
        <div className="mt-6 text-center">
          <div className="inline-flex items-center space-x-2 px-3 py-1 bg-danger-100 text-danger-800 rounded-full text-sm">
            <div className="w-2 h-2 bg-danger-500 rounded-full"></div>
            <span>Call Error</span>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-6 text-center">
        <div className="text-xs text-gray-500 space-y-1">
          {!isConnected && (
            <p>Please connect to the server first</p>
          )}
          {isConnected && callState === CALL_STATES.IDLE && (
            <p>Click the green phone button to start a call</p>
          )}
          {isInCall && (
            <>
              <p>Use the microphone button to mute/unmute</p>
              <p>Click the red phone button to end the call</p>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CallControls;
