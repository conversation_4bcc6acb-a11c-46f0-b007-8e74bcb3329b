import { useState, useEffect, useRef, useCallback } from 'react';
import toast from 'react-hot-toast';
import { audioProcessor, AudioQueue } from '../utils/audioProcessor';

// WebSocket connection states
export const CONNECTION_STATES = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  ERROR: 'error'
};

// Call states
export const CALL_STATES = {
  IDLE: 'idle',
  CONNECTING: 'connecting',
  IN_CALL: 'in_call',
  ENDING: 'ending',
  ENDED: 'ended',
  ERROR: 'error'
};

// Event types from the WebSocket protocol
const EVENT_TYPES = {
  CONNECTED: 'connected',
  START: 'start',
  MEDIA: 'media',
  STOP: 'stop',
  DTMF: 'dtmf',
  MARK: 'mark',
  CLEAR: 'clear'
};

// User management event types
const USER_EVENT_TYPES = {
  USER_REGISTER: 'user_register',
  CALL_START: 'call_start',
  CALL_END: 'call_end'
};

export const useVoiceSocket = (wsUrl = 'ws://localhost:5010') => {
  const [connectionState, setConnectionState] = useState(CONNECTION_STATES.DISCONNECTED);
  const [callState, setCallState] = useState(CALL_STATES.IDLE);
  const [streamId, setStreamId] = useState(null);
  const [sessionId, setSessionId] = useState(null);
  const [sequenceNumber, setSequenceNumber] = useState(0);
  
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 3000;

  // Audio processing refs
  const audioContextRef = useRef(null);
  const audioQueueRef = useRef(null);

  // Generate unique session ID
  const generateSessionId = useCallback(() => {
    return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }, []);

  // Initialize audio context
  const initializeAudioContext = useCallback(async () => {
    try {
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
        audioQueueRef.current = new AudioQueue(audioContextRef.current);
      }

      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }
    } catch (error) {
      console.error('Failed to initialize audio context:', error);
      toast.error('Failed to initialize audio system');
    }
  }, []);

  // Convert base64 to audio buffer and play
  const playAudioFromBase64 = useCallback(async (base64Data) => {
    try {
      if (!audioContextRef.current || !audioQueueRef.current) {
        await initializeAudioContext();
      }

      // Use the improved audio processor
      const audioBuffer = await audioProcessor.processReceivedAudio(base64Data, audioContextRef.current);

      if (audioBuffer && audioQueueRef.current) {
        audioQueueRef.current.enqueue(audioBuffer);
      }

    } catch (error) {
      console.error('Error playing audio:', error);
    }
  }, [initializeAudioContext]);

  // Handle incoming WebSocket messages
  const handleMessage = useCallback(async (event) => {
    try {
      const data = JSON.parse(event.data);
      
      // Handle protocol events
      if (data.event) {
        switch (data.event) {
          case EVENT_TYPES.CONNECTED:
            console.log('WebSocket protocol connected');
            setConnectionState(CONNECTION_STATES.CONNECTED);
            break;
            
          case EVENT_TYPES.START:
            console.log('Call started:', data);
            setStreamId(data.streamSid);
            setCallState(CALL_STATES.IN_CALL);
            toast.success('Call connected!');
            break;
            
          case EVENT_TYPES.MEDIA:
            // Handle incoming audio data
            if (data.media && data.media.payload) {
              await playAudioFromBase64(data.media.payload);
            }
            break;
            
          case EVENT_TYPES.STOP:
            console.log('Call stopped:', data);
            setCallState(CALL_STATES.ENDED);
            setStreamId(null);
            toast.success('Call ended');
            break;
            
          case EVENT_TYPES.MARK:
            console.log('Mark event received:', data);
            break;
            
          case EVENT_TYPES.CLEAR:
            console.log('Clear event received:', data);
            // Clear audio queue and processor buffer
            if (audioQueueRef.current) {
              audioQueueRef.current.clear();
            }
            audioProcessor.clearBuffer();
            break;
            
          default:
            console.log('Unknown protocol event:', data.event);
        }
      }
      
      // Handle user management events
      else if (data.type) {
        switch (data.type) {
          case 'user_registered':
            console.log('User registered successfully');
            toast.success('Connected to server');
            break;
            
          case 'error':
            console.error('Server error:', data.message);
            toast.error(data.message || 'Server error occurred');
            setCallState(CALL_STATES.ERROR);
            break;
            
          default:
            console.log('Unknown user event:', data.type);
        }
      }
      
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
    }
  }, [playAudioFromBase64]);

  // Connect to WebSocket
  const connect = useCallback(async () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      setConnectionState(CONNECTION_STATES.CONNECTING);
      
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = async () => {
        console.log('WebSocket connected');
        reconnectAttempts.current = 0;
        
        // Initialize audio context
        await initializeAudioContext();
        
        // Generate session ID and register user
        const newSessionId = generateSessionId();
        setSessionId(newSessionId);
        
        // Register user
        const registerMessage = {
          type: USER_EVENT_TYPES.USER_REGISTER,
          sessionId: newSessionId,
          userData: {
            name: 'Web User',
            mobile: '+1234567890',
            userId: 'web_user_' + Date.now(),
            sessionType: 'call'
          }
        };
        
        ws.send(JSON.stringify(registerMessage));
      };

      ws.onmessage = handleMessage;

      ws.onclose = (event) => {
        console.log('WebSocket closed:', event.code, event.reason);
        setConnectionState(CONNECTION_STATES.DISCONNECTED);
        setCallState(CALL_STATES.IDLE);
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          console.log(`Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectDelay);
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionState(CONNECTION_STATES.ERROR);
        toast.error('Connection error occurred');
      };

    } catch (error) {
      console.error('Failed to connect:', error);
      setConnectionState(CONNECTION_STATES.ERROR);
      toast.error('Failed to connect to server');
    }
  }, [wsUrl, handleMessage, initializeAudioContext, generateSessionId]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'User disconnected');
      wsRef.current = null;
    }
    
    setConnectionState(CONNECTION_STATES.DISCONNECTED);
    setCallState(CALL_STATES.IDLE);
    setStreamId(null);
    setSessionId(null);
  }, []);

  // Start a call
  const startCall = useCallback(() => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !sessionId) {
      toast.error('Not connected to server');
      return;
    }

    setCallState(CALL_STATES.CONNECTING);
    
    const startMessage = {
      type: USER_EVENT_TYPES.CALL_START,
      sessionId: sessionId
    };
    
    wsRef.current.send(JSON.stringify(startMessage));
  }, [sessionId]);

  // End a call
  const endCall = useCallback(() => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !sessionId) {
      return;
    }

    setCallState(CALL_STATES.ENDING);
    
    const endMessage = {
      type: USER_EVENT_TYPES.CALL_END,
      sessionId: sessionId,
      reason: 'user_ended'
    };
    
    wsRef.current.send(JSON.stringify(endMessage));
  }, [sessionId]);

  // Send audio data
  const sendAudioData = useCallback((audioData) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !streamId) {
      return;
    }

    try {
      // Use the improved audio processor to encode
      const base64Data = audioProcessor.encodeToBase64(new Uint8Array(audioData));

      const mediaMessage = {
        event: EVENT_TYPES.MEDIA,
        streamSid: streamId,
        sequenceNumber: (sequenceNumber + 1).toString(),
        media: {
          chunk: Math.floor(Date.now() / 20).toString(), // 20ms chunks
          timestamp: Date.now().toString(),
          payload: base64Data
        }
      };

      wsRef.current.send(JSON.stringify(mediaMessage));
      setSequenceNumber(prev => prev + 1);

    } catch (error) {
      console.error('Error sending audio data:', error);
    }
  }, [streamId, sequenceNumber]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [disconnect]);

  return {
    connectionState,
    callState,
    streamId,
    sessionId,
    connect,
    disconnect,
    startCall,
    endCall,
    sendAudioData,
    isConnected: connectionState === CONNECTION_STATES.CONNECTED,
    isInCall: callState === CALL_STATES.IN_CALL,
    isConnecting: callState === CALL_STATES.CONNECTING
  };
};
