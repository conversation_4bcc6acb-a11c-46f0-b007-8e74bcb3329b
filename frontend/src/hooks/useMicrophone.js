import { useState, useEffect, useRef, useCallback } from 'react';
import toast from 'react-hot-toast';
import { audioProcessor } from '../utils/audioProcessor';

// Microphone states
export const MIC_STATES = {
  IDLE: 'idle',
  REQUESTING: 'requesting',
  GRANTED: 'granted',
  DENIED: 'denied',
  ERROR: 'error'
};

export const useMicrophone = ({ onAudioData, enabled = false }) => {
  const [micState, setMicState] = useState(MIC_STATES.IDLE);
  const [isMuted, setIsMuted] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  
  const streamRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const processorRef = useRef(null);
  const animationFrameRef = useRef(null);

  // Audio processing configuration
  const BUFFER_SIZE = 4096;



  // Process audio data and send in chunks
  const processAudioData = useCallback((inputBuffer) => {
    if (!onAudioData || isMuted) return;

    const inputData = inputBuffer.getChannelData(0);

    // Use the improved audio processor
    const chunks = audioProcessor.processInputAudio(inputData, inputBuffer.sampleRate);

    // Send each chunk
    chunks.forEach(chunk => {
      onAudioData(chunk.buffer);
    });
  }, [onAudioData, isMuted]);

  // Calculate audio level for visualization
  const calculateAudioLevel = useCallback(() => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    analyserRef.current.getByteFrequencyData(dataArray);

    // Calculate RMS
    let sum = 0;
    for (let i = 0; i < dataArray.length; i++) {
      sum += dataArray[i] * dataArray[i];
    }
    const rms = Math.sqrt(sum / dataArray.length);
    const level = Math.min(100, (rms / 255) * 100);
    
    setAudioLevel(level);

    if (micState === MIC_STATES.GRANTED && !isMuted) {
      animationFrameRef.current = requestAnimationFrame(calculateAudioLevel);
    }
  }, [micState, isMuted]);

  // Initialize audio context and processing
  const initializeAudioProcessing = useCallback(async (stream) => {
    try {
      // Create audio context
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 44100 // Browser default, we'll resample to 8kHz
      });

      if (audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
      }

      // Create analyser for audio level visualization
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      analyserRef.current.smoothingTimeConstant = 0.8;

      // Create media stream source
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);

      // Create script processor for audio data
      processorRef.current = audioContextRef.current.createScriptProcessor(BUFFER_SIZE, 1, 1);
      
      processorRef.current.onaudioprocess = (event) => {
        processAudioData(event.inputBuffer);
      };

      // Connect the processing chain
      source.connect(processorRef.current);
      processorRef.current.connect(audioContextRef.current.destination);

      // Start audio level monitoring
      calculateAudioLevel();

    } catch (error) {
      console.error('Error initializing audio processing:', error);
      throw error;
    }
  }, [processAudioData, calculateAudioLevel]);

  // Request microphone permission and start capture
  const requestMicrophone = useCallback(async () => {
    if (micState === MIC_STATES.REQUESTING || micState === MIC_STATES.GRANTED) {
      return;
    }

    try {
      setMicState(MIC_STATES.REQUESTING);

      const constraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          channelCount: 1
        },
        video: false
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      await initializeAudioProcessing(stream);

      setMicState(MIC_STATES.GRANTED);
      toast.success('Microphone access granted');

    } catch (error) {
      console.error('Error requesting microphone:', error);
      
      if (error.name === 'NotAllowedError') {
        setMicState(MIC_STATES.DENIED);
        toast.error('Microphone access denied. Please allow microphone access and try again.');
      } else if (error.name === 'NotFoundError') {
        setMicState(MIC_STATES.ERROR);
        toast.error('No microphone found. Please connect a microphone and try again.');
      } else {
        setMicState(MIC_STATES.ERROR);
        toast.error('Failed to access microphone: ' + error.message);
      }
    }
  }, [micState, initializeAudioProcessing]);

  // Stop microphone capture
  const stopMicrophone = useCallback(() => {
    // Cancel animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Stop media stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Close audio context
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    // Reset refs
    analyserRef.current = null;
    processorRef.current = null;

    // Clear audio processor buffer
    audioProcessor.clearBuffer();

    setMicState(MIC_STATES.IDLE);
    setAudioLevel(0);
    setIsMuted(false);
  }, []);

  // Toggle mute state
  const toggleMute = useCallback(() => {
    if (micState !== MIC_STATES.GRANTED) return;

    setIsMuted(prev => {
      const newMuted = !prev;
      
      if (streamRef.current) {
        streamRef.current.getAudioTracks().forEach(track => {
          track.enabled = !newMuted;
        });
      }

      if (newMuted) {
        setAudioLevel(0);
        toast.success('Microphone muted');
      } else {
        toast.success('Microphone unmuted');
        calculateAudioLevel();
      }

      return newMuted;
    });
  }, [micState, calculateAudioLevel]);

  // Auto-start microphone when enabled
  useEffect(() => {
    if (enabled && micState === MIC_STATES.IDLE) {
      requestMicrophone();
    } else if (!enabled && micState === MIC_STATES.GRANTED) {
      stopMicrophone();
    }
  }, [enabled, micState, requestMicrophone, stopMicrophone]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopMicrophone();
    };
  }, [stopMicrophone]);

  return {
    micState,
    isMuted,
    audioLevel,
    isGranted: micState === MIC_STATES.GRANTED,
    isDenied: micState === MIC_STATES.DENIED,
    isRequesting: micState === MIC_STATES.REQUESTING,
    hasError: micState === MIC_STATES.ERROR,
    requestMicrophone,
    stopMicrophone,
    toggleMute
  };
};
