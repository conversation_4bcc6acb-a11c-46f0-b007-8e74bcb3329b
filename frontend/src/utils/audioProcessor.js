/**
 * Audio Processing Utilities for WebSocket Voice Protocol
 * Handles µ-law encoding/decoding, resampling, and audio format conversions
 */

// µ-law encoding/decoding tables for better performance
const MULAW_BIAS = 0x84;
const MULAW_MAX = 0x1FFF;
const MULAW_CLIP = 32635;

// Pre-computed µ-law encoding table
const muLawEncodeTable = new Array(256);
const muLawDecodeTable = new Array(256);

// Initialize µ-law tables
function initializeMuLawTables() {
  // Encoding table
  for (let i = 0; i < 256; i++) {
    let sample = i - 128;
    sample = sample * 256; // Scale to 16-bit range
    
    const sign = (sample >> 8) & 0x80;
    if (sign !== 0) sample = -sample;
    if (sample > MULAW_CLIP) sample = MULAW_CLIP;
    
    sample += MULAW_BIAS;
    let exponent = 7;
    for (let exp = 0; exp < 8; exp++) {
      if (sample <= (0x1F << (exp + 3))) {
        exponent = exp;
        break;
      }
    }
    
    const mantissa = (sample >> (exponent + 3)) & 0x0F;
    muLawEncodeTable[i] = ~(sign | (exponent << 4) | mantissa);
  }
  
  // Decoding table
  for (let i = 0; i < 256; i++) {
    const mulaw = ~i;
    const exponent = (mulaw & 0x70) >> 4;
    const mantissa = mulaw & 0x0F;
    let sample = mantissa << (exponent + 3);
    sample += MULAW_BIAS;
    if (exponent > 0) sample += (1 << (exponent + 2));
    
    muLawDecodeTable[i] = (mulaw & 0x80) ? -sample : sample;
  }
}

// Initialize tables on module load
initializeMuLawTables();

/**
 * Audio Processor Class
 */
export class AudioProcessor {
  constructor() {
    this.sampleRate = 8000; // Protocol requirement
    this.chunkDuration = 20; // 20ms chunks
    this.chunkSize = (this.sampleRate * this.chunkDuration) / 1000; // 160 samples
    this.audioBuffer = [];
  }

  /**
   * Convert PCM Float32Array to µ-law bytes
   * @param {Float32Array} pcmData - Input PCM data (-1.0 to 1.0)
   * @returns {Uint8Array} µ-law encoded data
   */
  pcmToMuLaw(pcmData) {
    const muLawData = new Uint8Array(pcmData.length);
    
    for (let i = 0; i < pcmData.length; i++) {
      // Clamp and scale to 16-bit range
      let sample = Math.max(-1, Math.min(1, pcmData[i]));
      sample = Math.round(sample * 32767);
      
      // Apply µ-law encoding
      const sign = sample < 0 ? 0x80 : 0x00;
      sample = Math.abs(sample);
      
      if (sample > MULAW_CLIP) sample = MULAW_CLIP;
      sample += MULAW_BIAS;
      
      let exponent = 7;
      for (let exp = 0; exp < 8; exp++) {
        if (sample <= (0x1F << (exp + 3))) {
          exponent = exp;
          break;
        }
      }
      
      const mantissa = (sample >> (exponent + 3)) & 0x0F;
      muLawData[i] = ~(sign | (exponent << 4) | mantissa);
    }
    
    return muLawData;
  }

  /**
   * Convert µ-law bytes to PCM Float32Array
   * @param {Uint8Array} muLawData - Input µ-law data
   * @returns {Float32Array} PCM data (-1.0 to 1.0)
   */
  muLawToPcm(muLawData) {
    const pcmData = new Float32Array(muLawData.length);
    
    for (let i = 0; i < muLawData.length; i++) {
      const sample = muLawDecodeTable[muLawData[i]];
      pcmData[i] = sample / 32768.0; // Normalize to -1.0 to 1.0
    }
    
    return pcmData;
  }

  /**
   * Resample audio data
   * @param {Float32Array} inputData - Input audio data
   * @param {number} inputSampleRate - Input sample rate
   * @param {number} outputSampleRate - Output sample rate
   * @returns {Float32Array} Resampled audio data
   */
  resample(inputData, inputSampleRate, outputSampleRate) {
    if (inputSampleRate === outputSampleRate) {
      return inputData;
    }

    const ratio = inputSampleRate / outputSampleRate;
    const outputLength = Math.floor(inputData.length / ratio);
    const outputData = new Float32Array(outputLength);

    for (let i = 0; i < outputLength; i++) {
      const sourceIndex = i * ratio;
      const index = Math.floor(sourceIndex);
      const fraction = sourceIndex - index;

      if (index + 1 < inputData.length) {
        // Linear interpolation
        outputData[i] = inputData[index] * (1 - fraction) + inputData[index + 1] * fraction;
      } else {
        outputData[i] = inputData[index];
      }
    }

    return outputData;
  }

  /**
   * Process audio input for transmission
   * @param {Float32Array} inputData - Raw audio input
   * @param {number} inputSampleRate - Input sample rate
   * @returns {Array<Uint8Array>} Array of µ-law encoded chunks
   */
  processInputAudio(inputData, inputSampleRate = 44100) {
    // Resample to 8kHz if needed
    let processedData = inputData;
    if (inputSampleRate !== this.sampleRate) {
      processedData = this.resample(inputData, inputSampleRate, this.sampleRate);
    }

    // Add to buffer
    this.audioBuffer.push(...processedData);

    // Extract chunks
    const chunks = [];
    while (this.audioBuffer.length >= this.chunkSize) {
      const chunk = this.audioBuffer.splice(0, this.chunkSize);
      const chunkArray = new Float32Array(chunk);
      const muLawChunk = this.pcmToMuLaw(chunkArray);
      chunks.push(muLawChunk);
    }

    return chunks;
  }

  /**
   * Process received audio for playback
   * @param {string} base64Data - Base64 encoded µ-law data
   * @returns {AudioBuffer} Web Audio API AudioBuffer
   */
  async processReceivedAudio(base64Data, audioContext) {
    try {
      // Decode base64 to µ-law data
      const binaryString = atob(base64Data);
      const muLawData = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        muLawData[i] = binaryString.charCodeAt(i);
      }

      // Convert µ-law to PCM
      const pcmData = this.muLawToPcm(muLawData);

      // Create AudioBuffer
      const audioBuffer = audioContext.createBuffer(1, pcmData.length, this.sampleRate);
      audioBuffer.getChannelData(0).set(pcmData);

      return audioBuffer;
    } catch (error) {
      console.error('Error processing received audio:', error);
      return null;
    }
  }

  /**
   * Convert audio data to base64 for transmission
   * @param {Uint8Array} muLawData - µ-law encoded data
   * @returns {string} Base64 encoded string
   */
  encodeToBase64(muLawData) {
    let binaryString = '';
    for (let i = 0; i < muLawData.length; i++) {
      binaryString += String.fromCharCode(muLawData[i]);
    }
    return btoa(binaryString);
  }

  /**
   * Calculate audio level (RMS) for visualization
   * @param {Float32Array} audioData - Audio data
   * @returns {number} Audio level (0-100)
   */
  calculateAudioLevel(audioData) {
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i];
    }
    const rms = Math.sqrt(sum / audioData.length);
    return Math.min(100, rms * 100);
  }

  /**
   * Apply noise gate to reduce background noise
   * @param {Float32Array} audioData - Input audio data
   * @param {number} threshold - Noise gate threshold (0-1)
   * @returns {Float32Array} Processed audio data
   */
  applyNoiseGate(audioData, threshold = 0.01) {
    const processedData = new Float32Array(audioData.length);
    
    for (let i = 0; i < audioData.length; i++) {
      const sample = Math.abs(audioData[i]);
      if (sample > threshold) {
        processedData[i] = audioData[i];
      } else {
        processedData[i] = 0;
      }
    }
    
    return processedData;
  }

  /**
   * Clear internal audio buffer
   */
  clearBuffer() {
    this.audioBuffer = [];
  }

  /**
   * Get buffer size
   * @returns {number} Current buffer size
   */
  getBufferSize() {
    return this.audioBuffer.length;
  }
}

/**
 * Audio Queue for managing playback timing
 */
export class AudioQueue {
  constructor(audioContext) {
    this.audioContext = audioContext;
    this.queue = [];
    this.isPlaying = false;
    this.nextStartTime = 0;
  }

  /**
   * Add audio buffer to queue
   * @param {AudioBuffer} audioBuffer - Audio buffer to queue
   */
  enqueue(audioBuffer) {
    this.queue.push(audioBuffer);
    if (!this.isPlaying) {
      this.playNext();
    }
  }

  /**
   * Play next audio buffer in queue
   */
  playNext() {
    if (this.queue.length === 0) {
      this.isPlaying = false;
      return;
    }

    this.isPlaying = true;
    const audioBuffer = this.queue.shift();
    
    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(this.audioContext.destination);

    const currentTime = this.audioContext.currentTime;
    const startTime = Math.max(currentTime, this.nextStartTime);
    
    source.start(startTime);
    this.nextStartTime = startTime + audioBuffer.duration;

    source.onended = () => {
      this.playNext();
    };
  }

  /**
   * Clear the audio queue
   */
  clear() {
    this.queue = [];
    this.isPlaying = false;
    this.nextStartTime = 0;
  }
}

// Export singleton instance
export const audioProcessor = new AudioProcessor();
