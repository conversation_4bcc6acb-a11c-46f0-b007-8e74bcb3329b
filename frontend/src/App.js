import React, { useState, useEffect, useCallback } from 'react';
import { Toaster } from 'react-hot-toast';
import { Settings, Headphones, Wifi } from 'lucide-react';

import { useVoiceSocket } from './hooks/useVoiceSocket';
import { useMicrophone } from './hooks/useMicrophone';
import CallControls from './components/CallControls';
import CallStatus from './components/CallStatus';

function App() {
  const [wsUrl, setWsUrl] = useState('ws://localhost:5010');
  const [showSettings, setShowSettings] = useState(false);
  const [autoConnect, setAutoConnect] = useState(true);

  // Initialize WebSocket connection
  const {
    connectionState,
    callState,
    streamId,
    sessionId,
    connect,
    disconnect,
    startCall,
    endCall,
    sendAudioData,
    isConnected,
    isInCall,
    isConnecting
  } = useVoiceSocket(wsUrl);

  // Initialize microphone with audio data callback
  const handleAudioData = useCallback((audioData) => {
    if (isInCall && audioData) {
      sendAudioData(audioData);
    }
  }, [isInCall, sendAudioData]);

  const {
    micState,
    isMuted,
    audioLevel,
    isGranted: isMicGranted,
    isDenied: isMicDenied,
    isRequesting: isMicRequesting,
    hasError: hasMicError,
    requestMicrophone,
    stopMicrophone,
    toggleMute
  } = useMicrophone({ 
    onAudioData: handleAudioData, 
    enabled: isInCall 
  });

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }
  }, [connect, autoConnect]);

  // Handle connection toggle
  const handleConnectionToggle = () => {
    if (isConnected) {
      disconnect();
    } else {
      connect();
    }
  };

  // Handle call start
  const handleStartCall = async () => {
    if (!isMicGranted) {
      await requestMicrophone();
    }
    startCall();
  };

  // Handle call end
  const handleEndCall = () => {
    endCall();
    stopMicrophone();
  };

  // Handle mute toggle
  const handleToggleMute = () => {
    toggleMute();
  };

  // Settings panel
  const SettingsPanel = () => (
    <div className="card p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Settings</h3>
        <button
          onClick={() => setShowSettings(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ×
        </button>
      </div>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            WebSocket URL
          </label>
          <input
            type="text"
            value={wsUrl}
            onChange={(e) => setWsUrl(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            placeholder="ws://localhost:5010"
          />
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="autoConnect"
            checked={autoConnect}
            onChange={(e) => setAutoConnect(e.target.checked)}
            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <label htmlFor="autoConnect" className="ml-2 block text-sm text-gray-900">
            Auto-connect on page load
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary-100 rounded-lg">
                <Headphones className="h-6 w-6 text-primary-600" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">AI Voice Mate</h1>
                <p className="text-sm text-gray-500">Real-time Voice Calling</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {/* Connection Status Indicator */}
              <div className="flex items-center space-x-2">
                <Wifi 
                  size={16} 
                  className={isConnected ? 'text-success-600' : 'text-gray-400'} 
                />
                <span className={`text-sm ${isConnected ? 'text-success-600' : 'text-gray-500'}`}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              
              {/* Settings Button */}
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Settings size={20} />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Settings Panel */}
        {showSettings && <SettingsPanel />}

        {/* Connection Controls */}
        <div className="card p-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Server Connection</h3>
              <p className="text-sm text-gray-500">
                {isConnected ? `Connected to ${wsUrl}` : `Disconnected from ${wsUrl}`}
              </p>
            </div>
            <button
              onClick={handleConnectionToggle}
              disabled={isConnecting}
              className={`px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                isConnected
                  ? 'bg-danger-600 hover:bg-danger-700 text-white focus:ring-danger-500'
                  : 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500'
              } ${isConnecting ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isConnecting ? 'Connecting...' : isConnected ? 'Disconnect' : 'Connect'}
            </button>
          </div>
        </div>

        {/* Main Interface Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Call Controls */}
          <div className="order-1 lg:order-1">
            <CallControls
              callState={callState}
              isConnected={isConnected}
              isMuted={isMuted}
              onStartCall={handleStartCall}
              onEndCall={handleEndCall}
              onToggleMute={handleToggleMute}
              audioLevel={audioLevel}
            />
          </div>

          {/* Call Status */}
          <div className="order-2 lg:order-2">
            <CallStatus
              connectionState={connectionState}
              callState={callState}
              micState={micState}
              sessionId={sessionId}
              streamId={streamId}
              audioLevel={audioLevel}
            />
          </div>
        </div>

        {/* Microphone Permissions Notice */}
        {isMicDenied && (
          <div className="mt-6 card p-4 bg-yellow-50 border-yellow-200">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Settings size={16} className="text-yellow-600" />
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-yellow-800">
                  Microphone Access Required
                </h3>
                <p className="text-sm text-yellow-700 mt-1">
                  To make voice calls, please allow microphone access in your browser settings and refresh the page.
                </p>
                <button
                  onClick={requestMicrophone}
                  className="mt-2 text-sm text-yellow-800 underline hover:text-yellow-900"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Error States */}
        {hasMicError && (
          <div className="mt-6 card p-4 bg-danger-50 border-danger-200">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-danger-100 rounded-full flex items-center justify-center">
                  <Settings size={16} className="text-danger-600" />
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-danger-800">
                  Microphone Error
                </h3>
                <p className="text-sm text-danger-700 mt-1">
                  There was an error accessing your microphone. Please check your device settings and try again.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">How to Use</h3>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium">1</span>
              <p>Ensure you're connected to the WebSocket server (green status indicator)</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium">2</span>
              <p>Allow microphone access when prompted by your browser</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium">3</span>
              <p>Click the green phone button to start a voice call</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium">4</span>
              <p>Use the microphone and speaker controls during the call</p>
            </div>
            <div className="flex items-start space-x-3">
              <span className="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium">5</span>
              <p>Click the red phone button to end the call</p>
            </div>
          </div>
        </div>
      </main>

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#10b981',
              secondary: '#fff',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
    </div>
  );
}

export default App;
